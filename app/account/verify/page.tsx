'use client'

import { Loader } from "@/common/components/molecules";
import {
  syncGuestUrl, verifyTokenUrl,
} from "@/common/utils/network/endpoints";
import { 
  useRouter, useSearchParams,
} from "next/navigation";
import { 
  useEffect, useRef,
} from "react";
import { Toaster } from "@/common/components/molecules";
import toast from "react-hot-toast";
import { routes } from "@/common/routes";
import { 
  signIn, useSession,
} from "next-auth/react";
import { parseObjectPropertiesToCamelCase } from "@/common/utils/helpers";
import lang from "@/common/lang";
import { showCaseLink } from "@/common/constants";
import { FbPixelScript } from "@/common/utils/fbPixel";
import {
  taboolaCustomEvent, TaboolaEventName, 
} from "@/common/utils/taboola/eventTriggers";
declare global {
  interface Window {
    fbq: (event: string, action: string, data: any) => void;
  }
}

const { verifyAccount: verifyAccountCopy } = lang

const VerifyPage = () => {
  const router = useRouter()
  const hasRun = useRef(false);
  const searchParams = useSearchParams()
  const { 
    update, data: session, status,
  } = useSession()
  
  const code = searchParams.get('code')
  useEffect(() => {
    const localUUID = localStorage.getItem('userUuid');
    const loginAndProceed = async () => {
      try {
        if (hasRun.current) {
          return;
        }
        hasRun.current = true;
        const response = await fetch(`${verifyTokenUrl}?code=${code}`, {
          method: 'POST',
          headers: {
            Accept: "application/json",
          },
        });
        const user = await response.json()
        if (user.data) {
          localStorage.setItem('loggedinUserUuid', user.data.uuid);
          if (localUUID && localUUID !== '') {
            fetch(`${syncGuestUrl}/${localUUID}`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${user.data.token}`,
              },
            }).then((response) => {
              if (response.ok) {
                localStorage.removeItem("userUuid");
                localStorage.removeItem("surveyAnswers");
              }
            })
          }
          await signIn("credentials", {
            ...parseObjectPropertiesToCamelCase(user.data),
            redirect: false,
          });
        }
        if (user.error) {
          router.push(routes.showcaseLogin)
          toast.error(verifyAccountCopy.verifyError)
        }
      } catch (error) {
        router.push(routes.showcaseLogin)
        toast.error(verifyAccountCopy.verifyError)
      }
    }
    loginAndProceed()
  }, [code, router, update])

  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      if (session.user.investorType) {
        const redirectPath = sessionStorage.getItem('redirect')
        if (sessionStorage.getItem('ssoTriggered') === 'true') {
          sessionStorage.removeItem('ssoTriggered')
          window.fbq('track', 'Lead', { 
            ...session?.user,
            source: 'Social Sign-in Success',
            page: redirectPath,
          });
          taboolaCustomEvent({
            eventName: TaboolaEventName.SSOSuccess,
            taboolaProps: { 
              ...session?.user,
              source: 'Social Sign-in Success',
              page: redirectPath,
            },
          });
        }
        if (redirectPath) {
          window.open(redirectPath, "_self")
        } else {
          window.open(showCaseLink, "_self")
        }
      } else {
        router.push(routes.userOnboarding)
      }
    }
  }, [session, status, router])
  return (
    <>
      <Loader />
      <FbPixelScript />
      <Toaster />
    </>
  )
}

export default VerifyPage;
