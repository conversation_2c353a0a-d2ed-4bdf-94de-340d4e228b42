import {
  useEffect, useState, 
} from 'react';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { v4 as uuidv4 } from "uuid";
import {
  mixpanelCustomEvent, MixpanelEventName, 
} from '@/common/utils/mixpanel/eventTriggers';
import * as CheckMark from '@/common/components/icons/CheckMark.json';
import Lottie from 'react-lottie';
import lang from '@/common/lang';
import {
  getSurvayUrl, postSurvayUrl, 
} from '@/common/utils/network/endpoints';

const { showcaseSurvay: {
  title, subtitle,
} } = lang;

type SurveySectionProps = {
  slug: string;
  questions?: Array<{
    id: number;
    question: string;
    options: Array<{
      id: number;
      text: string;
    }>;
  }>;
};

export const SurveySection = ({
  slug,
  questions = [],
}: SurveySectionProps) => {
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [openDropdown, setOpenDropdown] = useState<number | null>(null);
  const [userUuid, setUserUuid] = useState('');
  
  useEffect(() => {
    const uuid = localStorage.getItem('loggedinUserUuid') || localStorage.getItem('userUuid') || uuidv4();
    if (!localStorage.getItem('loggedinUserUuid') || localStorage.getItem('loggedinUserUuid') === '') {
      localStorage.setItem('userUuid', uuid);
    }
    setUserUuid(uuid);
  }, []);

  useEffect(() => {
    const answersFromLocalStorage = localStorage.getItem('surveyAnswers');
    if (answersFromLocalStorage && JSON.parse(answersFromLocalStorage) && Object.keys(JSON.parse(answersFromLocalStorage)).length > 0) {
      setAnswers(JSON.parse(answersFromLocalStorage));
    } else if (userUuid && userUuid !== '') {
      getSurvayData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userUuid]);

  const getSurvayData = async () => {
    try {
      const url = `${getSurvayUrl.replace('{slug}', slug)}/${userUuid}`;
      const response = await fetch(url, { next: { tags: ['showcase'] } });
      if (!response.ok) {
        throw new Error('Failed to fetch survey data');
      }
      const data = await response.json();
      if (data.data) {
        const mappedAnswers = data.data.reduce((acc: Record<number, string>, item: any) => {
          const matchingQuestion = questions.find(q => q.question === item.question);
          if (matchingQuestion) {
            acc[matchingQuestion.id] = item.answer;
          }
          return acc;
        }, {});

        setAnswers(mappedAnswers);
        localStorage.setItem('surveyAnswers', JSON.stringify(mappedAnswers));
      }
    } catch (error) {
      console.error('Error fetching survey data:', error);
    }
  };

  const submitData = async (question: string,  answer: string) => {
    const response = await fetch(postSurvayUrl.replace('{slug}', slug), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        uuid: userUuid,
        question,
        answer,
        question_type: 'select',
      }),
    });
    if (!response.ok) {
      throw new Error('Failed to track view');
    }
    return response.json();
  }

  const handleOptionSelect = (question: string, questionId:number, value: string) => {
    submitData(question, value)
      .then((res) => {
        if (res.error) {
          console.error('Error submitting survey data:', res.error);
          return;
        }
      })
    mixpanelCustomEvent({
      eventName: MixpanelEventName.surveyDataSubmitted,
      mixpanelProps: {
        uuid: userUuid,
        question,
        answer: value,
        showcaseSlug: slug,
      },
    });
    
    setAnswers(prev => ({
      ...prev,
      [questionId]: value,
    }));
    const surveyAnswers = JSON.parse(localStorage.getItem('surveyAnswers') || '{}');
    localStorage.setItem('surveyAnswers', JSON.stringify({
      ...surveyAnswers,
      [questionId]: value,
    }));
    setOpenDropdown(null);
  };

  const defaultOptions = {
    loop: false,
    autoplay: true,
    animationData: CheckMark,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  }

  return (
    <div className="max-w-[1240px] mx-auto w-full px-4 md:px-6">
      <div className="relative">
        <div className="w-full rounded-[4px] p-6 md:py-6 md:px-12 flex flex-col justify-center relative bg-[#1B1A1F]">
          <div className="h-[300px] hidden lg:block pointer-events-none w-[400px] absolute left-0 -translate-x-1 -translate-y-1/2 top-0 blur-[70px] bg-[#AD7EFF30] -rotate-0" style={{ clipPath: `inset(50% -50% -50% 1%)` }}></div>
          <div className="h-[300px] pointer-events-none w-[400px] absolute left-0 opacity-50 -translate-x-1 -translate-y-1/2 top-0 blur-[120px] bg-[#AD7EFF30] -rotate-0"></div>
            
          <div className="flex flex-col lg:flex-row gap-10">
            <div className="md:w-[320px] flex-shrink-0">
              <div className="flex items-start gap-4 justify-center flex-col">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path d="M9 2H5.8C4.11984 2 3.27976 2 2.63803 2.32698C2.07354 2.6146 1.6146 3.07354 1.32698 3.63803C1 4.27976 1 5.11984 1 6.8V12C1 12.93 1 13.395 1.10222 13.7765C1.37962 14.8117 2.18827 15.6204 3.22354 15.8978C3.60504 16 4.07003 16 5 16V18.3355C5 18.8684 5 19.1348 5.10923 19.2716C5.20422 19.3906 5.34827 19.4599 5.50054 19.4597C5.67563 19.4595 5.88367 19.2931 6.29976 18.9602L8.6852 17.0518C9.1725 16.662 9.4162 16.4671 9.6875 16.3285C9.9282 16.2055 10.1844 16.1156 10.4492 16.0613C10.7477 16 11.0597 16 11.6837 16H13.2C14.8802 16 15.7202 16 16.362 15.673C16.9265 15.3854 17.3854 14.9265 17.673 14.362C18 13.7202 18 12.8802 18 11.2V11M18.1213 1.87868C19.2929 3.05025 19.2929 4.94975 18.1213 6.12132C16.9497 7.29289 15.0503 7.29289 13.8787 6.12132C12.7071 4.94975 12.7071 3.05025 13.8787 1.87868C15.0503 0.707108 16.9497 0.707108 18.1213 1.87868Z" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <h2 className="text-xl md:text-2xl font-semibold text-white mb-2">{title}</h2>
                <p className="text-[#9CA3AF] text-sm">{subtitle}</p>
              </div>
            </div>

            <div className="flex-1 space-y-6">
              {questions.map((question, index) => (
                <div key={question.id} className="space-y-2">
                  <div className="flex items-center flex-col sm:flex-row gap-6 p-4 rounded-[4px] bg-[#28262E]">
                    <div className="flex gap-1 items-start flex-shrink-0 w-[320px]">
                      {answers[question.id] ? <Lottie options={defaultOptions} height={60} width={60} />
                        : <span className="text-white text-base mr-3">{index + 1}.</span>}
                      <p className="text-white text-base md:text-lg">{question.question}</p>
                    </div>
                    <div className="relative flex-1 w-full">
                      <button
                        onClick={() => setOpenDropdown(openDropdown === question.id ? null : question.id)}
                        className={`w-full min-h-[42px] bg-[#29282D] backdrop-blur-sm rounded-[4px] text-left flex items-center justify-between border border-[#737373] border-opacity-50 px-4 ${
                          answers[question.id] ? 'text-white' : 'text-[#9CA3AF]'
                        }`}
                      >
                        <span>{answers[question.id] || 'Select an option'}</span>
                        <span className={`transition-transform ${
                          openDropdown === question.id ? 'rotate-180' : ''
                        }`}><ChevronDown /></span>
                      </button>
                      {openDropdown === question.id && (
                        <motion.div
                          initial={{
                            opacity: 0,
                            y: -10, 
                          }}
                          animate={{
                            opacity: 1,
                            y: 0, 
                          }}
                          className="absolute z-[4] w-full mt-2 bg-[#2A2533]/60 backdrop-blur-sm rounded-lg shadow-lg border border-[#3A3543]/30 overflow-hidden"
                        >
                          {question.options.map((option) => (
                            <button
                              key={option.id}
                              onClick={() => handleOptionSelect(question.question, question.id, option.text)}
                              className="w-full p-4 text-white hover:bg-[#3A3543]/60 transition-colors text-left text-base"
                            >
                              {option.text}
                            </button>
                          ))}
                        </motion.div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};