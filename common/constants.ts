import { MemberRoleNamesEnum } from "@/app/cms/(protected)/team/types";
import lang from "./lang";

const {
  showcase: { copilot: copilotCopy },
} = lang;

export const mobileWidthLimit = 667;
export const tabletWidthLimit = 768;
export const emailRegex =
  /^(?!.*\.{2})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
export const urlRegex =
  /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/;
export const jikaUrlRegex =
  /^(?:https?:\/\/)?(?:www\.)?jika\.io\/embed\/area-chart(?:\?[^\s]*)?$/;
export const youtubeViemoUrlRegex =
  /http(?:s)?:\/\/(?:www\.)?(?:vimeo\.com\/([0-9]{5})|youtube\.com\/watch\?v=([^&]+)|youtu\.be\/([^?]+))/i;
export const youtubeFullUrlRegex =
  /http(?:s)?:\/\/(?:www\.)?(?:vimeo\.com\/(\d+)|youtube\.com\/watch\?v=([^&]+)(?:.*?[&?]t=(\d+))?|youtu\.be\/([^?&]+)(?:.*?[&?]t=(\d+))?)/i;
export const acceptedAvatarImageFileTypes = ".png,.jpg";
export const acceptedPDFFileTypes = "application/pdf";
export const mbInBytesBinary = 1024000;
export const maxImageSizeInMb = 10;
export const maxPDFSizeInMb = 20;
export const maxImageSizeInBytes = maxImageSizeInMb * mbInBytesBinary;
export const maxPDFSizeInBytes = maxPDFSizeInMb * mbInBytesBinary;
export const acceptedImageMimeTypes = ["image/jpeg", "image/png"];
export const digitRegex = /^\d+$/;

export const allowedBase64ImageFileSignatures = {
  png: "iVBORw0KGgo",
  jpeg: "/9j/",
  jpg: "/9j/",
};
export const columnOrderTypes = {
  asc: "asc",
  desc: "desc",
};
export const dateFormats = {
  default: "DD MMM YYYY",
  iso: "YYYY-MM-DD",
  monthYear: "MMM YYYY",
  monthYearShort: "MM/YY",
  dayMonthYearShort: "DD/MM/YYYY",
};
export const roles = {
  admin: "super_admin",
  member: "user",
};

export const rolesMap = {
  [roles.admin]: MemberRoleNamesEnum.ADMIN,
  [roles.member]: MemberRoleNamesEnum.MEMBER,
};

export const authToken = "authToken";
export const loggedinUserUuid = "loggedinUserUuid";
export const surveyAnswers = "surveyAnswers";

export const defaultFeatureFlags = {
  notificationsFeature: false,
  profileManagementFeature: false,
  stripeFeature: false,
  teamFeature: false,
  cookiePolicyBannerFeature: false,
};

export const sessionStatuses = {
  AUTHENTICATED: "authenticated",
  UNAUTHENTICATED: "unauthenticated",
  LOADING: "loading",
};

export enum Environments {
  DEVELOPMENT = "development",
  PRODUCTION = "production",
}

export const awsProductionLink =
  "https://curation-api-production-assets.s3.eu-west-2.amazonaws.com/";
export const awsStagingLink =
  "https://curation-api-staging-assets.s3.eu-west-2.amazonaws.com/";
export const isProductionEnv =
  process.env.NEXT_PUBLIC_ENV === Environments.PRODUCTION;
export const isCookieYesEnabled =
  process.env.NEXT_PUBLIC_COOKIE_YES_ENABLE === "true";
export const isHotjarEnabled = process.env.NEXT_PUBLIC_HOTJAR_ENABLE === "true";
export const isRedditPixelEnabled =
  process.env.NEXT_PUBLIC_REDDIT_PIXEL_ENABLE === "true";
export const isTaboolaEnabled =
  process.env.NEXT_PUBLIC_TABOOLA_ENABLE === "true";
export const isCustomPromptsDisabled =
  process.env.NEXT_PUBLIC_DISABLE_FEATURE === "off";
export const isRateStockEnabled = 
  process.env.NEXT_PUBLIC_SHOWCASE_SHOWCASES_RATE_STOCK === "on";
export const isSurvayEnabled = 
  process.env.NEXT_PUBLIC_SURVAY_FEATURE === "on";

export const homePageLink = process.env.NEXT_PUBLIC_HOME_PAGE_LINK || "";

export const showCaseLink = "https://app.curationconnect.com/";

export const jikaLink =
  "https://jika.io/embed/area-chart?symbol=AAPL&selection=one_year";

export const HeapAnalyticsProdId =
  process.env.NEXT_PUBLIC_HEAP_ANALYTICS_PROD_ID || "";
export const HeapAnalyticsDevId =
  process.env.NEXT_PUBLIC_HEAP_ANALYTICS_DEV_ID || "";

export const HotjarIds = {
  SHOWCASE: 4997408,
  MAIN: 5001481,
};

export const endPoints = {
  company: "/company",
};

export const randomUserIdKey = "randomUserId";
export const userEmailKey = "userEmail";

export const copilotQuestions = [
  copilotCopy.questionOne,
  copilotCopy.questionTwo,
  copilotCopy.questionThree,
  copilotCopy.questionFour,
  copilotCopy.questionFive,
  copilotCopy.questionSix,
  copilotCopy.questionSeven,
];

export const copilotUserRole = "user";
export const copilotAssistantRole = "assistant";

export const spotifyUrl = "https://open.spotify.com";
export const linkedInURL =
  "https://www.linkedin.com/company/curation-corporation";
export const youtubeURL = "https://www.youtube.com/@curationconnect";
export const subscribeURL =
  "https://share.hsforms.com/1CT6A1cHnRFuzIcJvMBS8Ow3l2f8";
export const termsUrl = "https://www.curationconnect.com/terms-of-use";
export const privacyPolicyUrl =
  "https://www.curationconnect.com/privacy-policy";

export const showcasePageMixpanel = "Showcase";
export const contactEmail = "<EMAIL>";

export const cookieYesScript =
  "https://cdn-cookieyes.com/client_data/e0286c8a565e3a04bb0add1a/script.js";

export const showcaseInvestorSignupURL = "/account/signup?showcase=";

export const showcaseCardTypes = {
  WITH_CAPTION: "with_caption",
  NUMBERED: "numbered",
  WITH_CATEGORY_TAG: "with_category_tag",
  COMING_SOON: "coming_soon",
  LOCKED: "locked",
};

export const SHAREFLIX_ALLOWED_PATHS = [
  "/shareflix",
  "/shareflix/",
  "/shareflix/verify-otp",
  "/shareflix/success",
  "/shareflix/onboarding",
];

export const SHAREFLIX_PATH_MAPPING: Record<string, string> = {
  "/": "/shareflix/",
  "/verify-otp": "/shareflix/verify-otp",
  "/success": "/shareflix/success",
  "/onboarding": "/shareflix/onboarding",
};

export const survayQuestions = [
  {
    id: 1,
    question: "What is your typical investment horizon?",
    options: [
      {
        id: 1,
        text: "Less then 1 year", 
      },
      {
        id: 2,
        text: "1-3 years", 
      },
      {
        id: 3,
        text: "3-5 years", 
      },
      {
        id: 4,
        text: "5+ years", 
      },
    ],
  },
  {
    id: 2,
    question: "What is your risk profile as an investor?",
    options: [
      {
        id: 1,
        text: "I don't want to loose money", 
      },
      {
        id: 2,
        text: "I will balance risk and reward", 
      },
      {
        id: 3,
        text: "I am willing to loose capital to seek greater returns", 
      },
    ],
  },
];