
import NextAuth, {
  FeatureFlags, NextAuthOptions,
} from 'next-auth';
import CredentialsProvider from "next-auth/providers/credentials"
import {
  featureFlagUrl, signInUrl,
} from "@/common/utils/network/endpoints";
import { parseObjectPropertiesToCamelCase } from "@/common/utils/helpers";
import { handleResponse } from "@/common/utils/network/responseHandler";
import { httpRequestMethods } from "@/common/utils/network/constants";

const { POST } = httpRequestMethods;

const OPTIONS: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Sign in',
      credentials: {
        email: {
          label: "Email",
          type: "text",
          placeholder: "<EMAIL>",
        },
        password: {
          label: "Password",
          type: "password",
          optional: true,
        },
        token: {
          label: "Token",
          type: "text",
          optional: true,
        },
        firstName: {
          label: "First Name",
          type: "text",
          optional: true,
        },
        lastName: {
          label: "Last Name",
          type: "text",
          optional: true,
        },
        isOnboarded: {
          label: "Onboarded",
          type: "boolean",
          optional: true,
        },
        emailVerifiedAt: {
          label: 'Em<PERSON> verified at',
          type: "string",
          optional: true,
        },
        id: {
          label: "Id",
          type: "number",
          optional: true,
        },
        loggedInAsSuperadmin: {
          label: "Logged in as superadmin",
          type: "text",
          optional: true,
        },
        investorType: {
          label: "Investor Type",
          type: "text",
          optional: true,
        },
        role: {
          label: "Role",
          type: "text",
          optional: true,
        },
        uuid: {
          label: "UUID",
          type: "text",
          optional: true,
        },
      },
      type: 'credentials',
      async authorize (credentials) {
        if (!credentials) {
          return null;
        }
        if (credentials.token) {
          return {
            token: credentials.token,
            email: credentials.email,
            firstName: credentials.firstName,
            lastName: credentials.lastName,
            id: credentials.id,
            isOnboarded: credentials.isOnboarded,
            investorType: credentials.investorType !== "undefined" ? credentials.investorType : '',
            role: credentials.role || '',
            emailVerifiedAt: credentials.emailVerifiedAt,
            loggedInAsSuperadmin: credentials.loggedInAsSuperadmin,
            uuid: credentials.uuid,
          };
        }
        const res = await fetch(signInUrl, {
          method: POST,
          body: JSON.stringify(credentials),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
          },
        });
        const {
          code, response,
        } = await handleResponse(res);
        const user = response;
        if (code === 200 && user) {
          return parseObjectPropertiesToCamelCase(user.data);
        }
        return null
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 14 * 24 * 60 * 60,
  },
  callbacks: {
    async jwt ({
      token, user,
    }) {
      if (user) {
        return {
          ...token,
          token: user.token,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          isOnboarded: user.onboarded,
          emailVerifiedAt: user.emailVerifiedAt,
          loggedInAsSuperadmin: user.loggedInAsSuperadmin || false,
          uuid: user.uuid,
          investorType: user.investorType,
          role: user.role,
        };
      }
      return token;
    },
    async session ({
      session, token,
    }) {
      const response = await fetch(featureFlagUrl, {
        method: 'GET',
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
      });

      const { response: featureFlagsResponse } = await handleResponse(response);
      session.featureFlags = featureFlagsResponse as FeatureFlags;

      if (token) {
        session.token = token.token as string;
        session.user.email = token.email as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.isOnboarded = token.isOnboarded as boolean;
        session.user.emailVerifiedAt = token.emailVerifiedAt as string;
        session.user.loggedInAsSuperadmin = token.loggedInAsSuperadmin as boolean;
        session.user.uuid = token.uuid as string;
        session.user.investorType = token.investorType as string;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/cms/signin',
    signOut: '/cms/signout',
  },
};

const handler = NextAuth(OPTIONS);

export {
  handler as GET, handler as POST,
};
