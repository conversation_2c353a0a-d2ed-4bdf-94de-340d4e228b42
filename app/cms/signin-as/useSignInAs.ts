

import useS<PERSON> from "swr";

import { signAsInUrl } from "@/common/utils/network/endpoints";
import { 
  getLocalStorageItem, parseObjectPropertiesToSnakeCase, removeLocalStorageItem,
} from "@/common/utils/helpers";
import { fetcher } from "@/common/utils/network/baseFetcher";
import { 
  signIn, useSession,
} from "next-auth/react";
import {
  httpRequestMethods,
  httpResponseStatuses,
} from "@/common/utils/network/constants";
import {
  useEffect,
} from "react";
import { useRouter } from "next/navigation";
import { routes } from "@/common/routes";
import { authToken } from "@/common/constants";

const { POST } = httpRequestMethods;


export const useSignInAs = ({ token } : { token: string }) => {
  const router = useRouter()
  const { data: session } = useSession()
 
  const signInFetcher = async (key: string) => {
    return fetcher(key, {
      arg: {
        method: POST,
        body: parseObjectPropertiesToSnakeCase({
          login_token: token,
        }),
      },
    })
  }

  const result = useSWR(
    token ? signAsInUrl : null, signInFetcher,
    { revalidateOnFocus: false },
  );

  const { 
    data: res,
    error,
    isLoading,
  } = result

  useEffect(() => {
    if (session) {
      if (!session.user?.emailVerifiedAt) {
        return router.push(routes.accountSignupVerifyPath);
      }
      router.push(routes.dashboardPath);
    }

    if (getLocalStorageItem(authToken)) {
      removeLocalStorageItem(authToken);
    }

  }, [session, router]);

  useEffect(() => {
    const triggerSignIn = async () => {
      if (res && res.code === httpResponseStatuses.OK) {
        const user = res.data
        await signIn("credentials", {
          id: user.id,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email,
          token: user.token,
          isOnboarded: user.onboarded,
          emailVerifiedAt: user.email_verified_at,
          uuid: user.uuid,
          loggedInAsSuperadmin: true,
        }).then(() => {
          router.push(routes.dashboardPath);
        });
      }
    }
    triggerSignIn()
  }, [res, router])

  return {
    isLoading: isLoading,
    error: error?.message || '',
  }
}