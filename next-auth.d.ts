import { DefaultSession } from "next-auth"

declare module "next-auth" {
  interface FeatureFlags {
    notificationsFeature: boolean,
    profileManagementFeature: boolean,
    stripeFeature: boolean,
    teamFeature: boolean,
    cookiePolicyBannerFeature: boolean,
  }
  export interface Session {
    token: string;
    featureFlags: FeatureFlags;
    user: {
      email: string;
      firstName: string;
      lastName: string;
      isOnboarded: boolean;
      emailVerifiedAt: string;
      loggedInAsSuperadmin: boolean;
      uuid: string;
      investorType: string | null;
      role: string;
    } & DefaultSession["user"]
  }
  export interface User {
    token: string;
    email: string;
    firstName: string;
    lastName: string;
    onboarded: boolean;
    emailVerifiedAt: string;
    uuid: string;
    loggedInAsSuperadmin: boolean;
    investorType: string;
    role: string;
  }
}
