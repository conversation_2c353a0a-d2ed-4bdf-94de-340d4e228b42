export const apiMatcher = "api/v1";
export const apiRootUrl =
  `${process.env.NEXT_PUBLIC_API_URL}/${apiMatcher}` ||
  `http://localhost:1010/${apiMatcher}`;
export const mixpanelToken = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
export const extractMetaUrl = `${process.env.NEXT_PUBLIC_API_URL}/extract`;
export const signInUrl = `${apiRootUrl}/login`;
export const signAsInUrl = `${apiRootUrl}/login-as`;
export const signUpUrl = `${apiRootUrl}/register`;
export const forgotPasswordUrl = `${apiRootUrl}/forgot-password`;
export const resetPasswordUrl = `${apiRootUrl}/reset-password`;
export const createPasswordUrl = `${apiRootUrl}/register`;
export const logoutUrl = `${apiRootUrl}/logout`;
export const userDetailsUrl = `${apiRootUrl}/profile`;
export const userSubscriptionUrl = `${userDetailsUrl}/subscription`;
export const userImageUrl = `${userDetailsUrl}/image`;
export const userImageUploadStatusUrl = `${userDetailsUrl}/image/status`;
export const notificationsUrl = `${userDetailsUrl}/notifications`;
export const usersUrl = `${apiRootUrl}/users`;
export const invitationsUrl = `${apiRootUrl}/invitations`;
export const featureFlagUrl = `${apiRootUrl}/features`;
export const membersCountUrl = `${apiRootUrl}/members/count`;

export const showcaseUrl = `${apiRootUrl}/showcases`;
export const showcaseLinksUrl = `${apiRootUrl}/showcases/{id}/links`;
export const showcaseDiscoveryUrl = `${apiRootUrl}/showcases/{id}/discovery-step`;
export const showcaseKeyInfoUrl = `${apiRootUrl}/showcases/{id}/key-info`;
export const showcaseCeoInterviewUrl = `${apiRootUrl}/showcases/{id}/exec-summary`;
export const showcaseCurationAiUrl = `${apiRootUrl}/showcases/{id}/enable-curation-ai`;
export const showcaseCurationAiVectorDBUrl = `${apiRootUrl}/showcases/{id}/ai-chat-files/assign`;
export const showcaseFaqsUrl = `${apiRootUrl}/showcases/{id}/faqs`;
export const showcaseCompanyContentsUrl = `${apiRootUrl}/showcases/{id}/company-contents`;
export const showcaseExternalInsightsUrl = `${apiRootUrl}/showcases/{id}/external-insights`;
export const showcaseExpertsUrl = `${apiRootUrl}/showcases/{id}/experts`;
export const showcaseKeyInfluencersUrl = `${apiRootUrl}/showcases/{id}/key-influencers`;
export const showcaseResearchArticlesUrl = `${apiRootUrl}/showcases/{id}/research-articles`;
export const showcaseTeamMembersUrl = `${apiRootUrl}/showcases/{id}/team-members`;
export const showcasePreviewUrl = `${apiRootUrl}/showcases/{id}/preview`;
export const showcasePendingApprovalUrl = `${apiRootUrl}/showcases/{id}/pending-approval`;
export const showcaseImageUploadUrl = `${apiRootUrl}/images`;
export const showcasesUrl = `${apiRootUrl}/showcases`;
export const showcasePublicURL = `${apiRootUrl}/showcase/{slug}`;
export const showcaseContactUsUrl = `${apiRootUrl}/contact`;

export const createShowcaseDraftUrl = `${apiRootUrl}/showcases/{id}/draft`;

export const publishDraftShowcaseUrl = `${apiRootUrl}/showcases/{id}/draft/publish`;

export const showcaseTypeUrl = `${apiRootUrl}/showcase-type/{slug}`;

export const passwordProtectedShowcaseUrl = `${apiRootUrl}/password-protected-showcase/{slug}`;

export const showcaseDeleteUrl = `${apiRootUrl}/showcases/{id}`;

export const adminPanelLink = `${process.env.NEXT_PUBLIC_API_URL}/admin`;

export const showcaseVisitorUrl = `${apiRootUrl}/showcase/{slug}/visitor`;
export const sendPrompt = `${apiRootUrl}/showcase/{id}/chat/send-message`;
export const getPromptsUrl = `${apiRootUrl}/showcase/{id}/chat/{thread_id}`;
export const tickersUrl = `${apiRootUrl}/tickers`;
export const industriesUrl = `${apiRootUrl}/industries`;

export const getShowcaseDocsUrl = `${apiRootUrl}/showcases/{id}/ai-chat-files`;
export const addPDFToShowcaseUrl = `${apiRootUrl}/showcases/{id}/ai-chat-files`;
export const deletePDFFromShowcaseUrl = `${apiRootUrl}/showcases/{id}/ai-chat-files/{fileId}`;

export const providersUrl = `${apiRootUrl}/auth/providers`;
export const redirectUrls = `${apiRootUrl}/auth/{provider}/redirect`;
export const verifyTokenUrl = `${apiRootUrl}/auth/verify-social-user`;
export const uploadFileToAIUrl = `https://api.openai.com/v1/files`;

export const subscriberSignup = `${apiRootUrl}/subscriber-signup`;
export const subscriberOnboarding = `${apiRootUrl}/subscriber-onboarding`;
export const preOnboarding = `${apiRootUrl}/subscriber-preonboarding`;
export const subscriberSignupInfo = `${apiRootUrl}/subscriber-signup-info`;
export const verifyOtp = `${apiRootUrl}/verify-otp`;
export const resendOtp = `${apiRootUrl}/resend/verification-otp`;
export const followStock = `${apiRootUrl}/showcases/{id}/subscribe`;
export const hasSubscribedUrl = `${apiRootUrl}/showcases/{id}/has-subscribed`;
export const disclaimerUrl = `${apiRootUrl}/showcases/{id}/disclaimer`;
export const stockSymbolUpdateUrl = `${apiRootUrl}/showcases/{id}/stock-symbol`;
export const searchSymbolUrl = `${apiRootUrl}/showcases/{id}/search-symbol`;
export const stockDataRecentUrl = `${apiRootUrl}/showcases/{slug}/stock-data/recent`;
export const stockDataDailyUrl = `${apiRootUrl}/showcases/{slug}/stock-data/daily`;
export const stockDataHourlyUrl = `${apiRootUrl}/showcases/{slug}/stock-data/hourly`;
export const stockDataWeeklyUrl = `${apiRootUrl}/showcases/{slug}/stock-data/weekly`;
export const stockDataMonthlyUrl = `${apiRootUrl}/showcases/{slug}/stock-data/monthly`;
export const getDiscoveryShowcasesUrl = `${apiRootUrl}/discovery?search=%searchTerm%`;
export const getFeaturedShowcasesUrl = `${apiRootUrl}/discovery`;
export const getContinueResearchShowcasesUrl = `${apiRootUrl}/continue-research`;
export const requestShowcaseUrl = `${apiRootUrl}/request-showcase`;
export const filterOptionsUrl = `${apiRootUrl}/discovery-filter-options`;
export const fetchWatchlistUrl = `${apiRootUrl}/subscriber-watchlist`;
export const deleteWatchlistUrl = `${apiRootUrl}/subscriber-watchlist/{id}`;
export const addToWatchlistUrl = `${apiRootUrl}/subscriber-watchlist`;
export const updateWatchlistFromLocalUrl = `${apiRootUrl}/subscriber-preonboarding`;
export const stockQuoteUrl = `${apiRootUrl}/showcases/{slug}/stock-data/quote`;
export const addContinueWatchingUrl = `${apiRootUrl}/showcases/{slug}/view`;
export const postSurvayUrl = `${apiRootUrl}/showcase/{slug}/survey-question`;
export const getSurvayUrl = `${apiRootUrl}/showcase/{slug}/survey`;
export const syncGuestUrl = `${apiRootUrl}/sync-guest`;
