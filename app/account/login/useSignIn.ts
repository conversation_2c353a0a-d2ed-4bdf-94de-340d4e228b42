import { 
  useEffect, useState,
} from "react";
import { SubmitHandler } from "react-hook-form";
import { toast } from "react-hot-toast";
import { 
  signIn, useSession,
} from "next-auth/react";
import { SignInDTO } from "@/app/cms/types";
import { showCaseLink } from "@/common/constants";
import lang from "@/common/lang";
import { handleFetchError } from "@/common/utils/network/errorHandler";
import { syncGuestUrl } from "@/common/utils/network/endpoints";
const {
  home, signUp: signUpCopy,  
} = lang;


export const useSignIn = () => {
  const { 
    data: session, 
    status,
  } = useSession()

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (session?.user.role === "subscriber" && status === "authenticated") {
      const localUUID = localStorage.getItem("userUuid");
      localStorage.setItem("loggedinUserUuid", session?.user.uuid);
      if (localUUID && localUUID !== "") {
        fetch(`${syncGuestUrl}/${localUUID}`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${session.token}`,
          },
        }).then((response) => {
          if (response.ok) {
            localStorage.removeItem("userUuid");
            localStorage.removeItem("surveyAnswers");
          }
        });
      }
      const redirectPath = sessionStorage.getItem("redirect");
      window.open(redirectPath ? redirectPath : showCaseLink, "_self");
    }
  }, [session, status]);

  const onSubmit: SubmitHandler<SignInDTO> = async (data) => {
    setIsLoading(true);

    try {
      const res = await signIn("credentials", {
        ...data,
        redirect: false,
      });
      if (res?.ok) {
        toast.success(home.loggedIn)
      }
      if (res?.error) {
        toast.error(res.error)
      }
    } catch (error) {
      handleFetchError(error, signUpCopy.errorLogingUp);
      
    }  
    setIsLoading(false);
  };

  return {
    onSubmit,
    isLoading: isLoading,
  }
}
