'use client'
import MixpanelPageView from "@/app/account/MixpanelPageView/MixpanelPageView";
import {
  Button,
  Typography,
} from "@/common/components/atoms";
import { ShareflixLogoIcon } from "@/common/components/icons";
import { showCaseLink } from "@/common/constants";
import lang from "@/common/lang";
import { routes } from "@/common/routes";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const { signUp: signUpCopy } = lang;

const Success = () => {
  const { status } = useSession()
  const router = useRouter()
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push(routes.showcaseLogin)
    }
  }, [status, router])

  const handleRedirect = () => {
    const cameFromRateShowcase = localStorage.getItem('cameFromRateShowcase');
    const redirectPath = sessionStorage.getItem('redirect');

    if (cameFromRateShowcase === 'true' && redirectPath) {
      localStorage.setItem('openStockRatingModal', 'true');
    }

    window.open(redirectPath ? redirectPath : showCaseLink, "_self");
  };

  return (
    <div className="min-h-screen flex justify-center items-center bg-[#0a0a0b] px-6 sm:px-8 py-6">
      <MixpanelPageView title="Onboarding success | Investor" />
      <div className="w-full max-w-sm md:max-w-md flex items-center flex-col text-white">
        <ShareflixLogoIcon />
        <Typography variant="h1" className={`mt-12 mb-3 font-semibold text-3xl text-center text-white`}>
          {signUpCopy.signUpCompleted}
        </Typography>
        <Button
          type="button"
          variant="white"
          size="md"
          onClick={handleRedirect}
          data-cy="submit-button"
          className="hover:bg-white/90 mt-4"
          width="w-full"
        >
          {signUpCopy.submitButtonLabel}
        </Button>
      </div>
    </div>
  )
}

export default Success;
