'use client';

import { 
  useEffect, useRef,
} from 'react';
import { useParams } from 'next/navigation';
import { 
  mixpanelCustomEvent, MixpanelEventName,
} from '@/common/utils/mixpanel/eventTriggers';

type SectionId =
  | 'stocks'
  | 'executive-summary'
  | 'key-info'
  | 'experts'
  | 'scroll-dark-banner'
  | 'scroll-faq'
  | 'investor-materials'
  | 'external-insights'
  | 'light-banner'
  | 'company-research'
  | 'team'
  | 'lets-connect'
  | 'footer';


const SectionTracker = () => {
  const { slug } = useParams<{ slug: string }>();
  const observersRef = useRef<Map<string, IntersectionObserver>>(new Map());
  const trackedSectionsRef = useRef<Set<string>>(new Set());


  useEffect(() => {
    const sectionIds: SectionId[] = [
      'stocks',
      'executive-summary',
      'key-info',
      'experts',
      'scroll-dark-banner',
      'scroll-faq',
      'investor-materials',
      'external-insights',
      'light-banner',
      'company-research',
      'team',
      'lets-connect',
      'footer',
    ];
    sectionIds.forEach(sectionId => {
      const element = document.getElementById(sectionId);

      if (!element) {
        console.warn(`Element with ID "${sectionId}" not found`);
        return;
      }

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && !trackedSectionsRef.current.has(sectionId)) {
            mixpanelCustomEvent({
              eventName: MixpanelEventName.sectionViewed,
              mixpanelProps: {
                section: sectionId,
                page: 'Showcase',
                slug,
              },
            });

            trackedSectionsRef.current.add(sectionId);

            observer.disconnect();
            observersRef.current.delete(sectionId);
          }
        },
        { threshold: 0.2 },
      );

      observer.observe(element);
      observersRef.current.set(sectionId, observer);
    });

    return () => {
      observersRef.current.forEach(observer => {
        observer.disconnect();
      });
      observersRef.current.clear();
    };
  }, [slug]);

  return null;
};

export default SectionTracker;
