/* eslint-disable no-shadow */
import { Dict } from "mixpanel-browser";

export enum MixpanelEventName {
  register = "Register",
  pageView = "Pageview",
  headerLinkClicked = "Header Link Clicked",
  copilotLinkClicked = "Curation AI Navigated Item Clicked",
  copilotOpened = "Curation AI Opened",
  copilotExpanded = "Curation AI Expanded",
  copilotClosed = "Curation AI Closed",
  questionsOpened = "Questions Opened",
  questionForPrompt = "Question Selected from Curation AI",
  customPrompt = "Custom question from Curation AI",
  followUsClicked = "Follow Us CTA Clicked",
  rateShowcaseClicked = "Rate Showcase CTA Clicked",
  watchlistPopupClicked = "+ Watchlist Pop - Go To Watchlist",
  addToWatchlistDiscoClicked = "Add To Watchlist Disco Clicked",
  heroSectionOpened = "Hero Section Opened",
  heroVideoPlayed = "Hero Video Played",
  stayUpdatedPopupOpened = "Stay Updated Popup Opened",
  socialSigninClicked = "Social Sign-in Clicked",
  stayUpdatedPopupClosed = "Stay Updated Popup Closed",
  sectionViewed = "Showcase Section Viewed",
  investmentThesisTabClicked = "Investment Thesis Tab Clicked",
  executiveSummaryTabClickedIT = "Executive Summary - Investment Thesis Tab Clicked",
  whyInvestTabClickedIT = "Why Invest - Investment Thesis Tab Clicked",
  keyRiskTabClickedIT = "Key Risk - Investment Thesis Tab Clicked",
  catalystsTabClickedIT = "Catalysts - Investment Thesis Tab Clicked",
  ceoInterviewTabClickedIT = "CEO Interview - Investment Thesis Tab Clicked",
  ceoInterviewButtonClickedIT = "CEO Interview Button Clicked",
  executiveSummaryCTAClicked = "Executive Summary CTA Clicked",
  catalystsTabClicked = "Catalysts Tab Clicked",
  catalystsShortTermTabClicked = "Catalysts Short Term Tab Clicked",
  catalystsMediumTermTabClicked = "Catalysts Medium Term Tab Clicked",
  catalystsLongTermTabClicked = "Catalysts Long Term Tab Clicked",
  expertSelected = "Expert Selected",
  expertSocialIconClicked = "Expert Social Icon Clicked",
  expertContentCTAClicked = "Expert Content CTA Clicked",
  expertContentSocialIconClicked = "Expert Content Social Icon Clicked",
  expertContentNavigationDotsClicked = "Expert Content Navigation Dots Clicked",
  expertContentNavigationLeftClicked = "Expert Content Navigation Left Clicked",
  expertContentNavigationRightClicked = "Expert Content Navigation Right Clicked",
  FAQQuestionOpened = "FAQ Question Opened",
  FAQQuestionClosed = "FAQ Question Closed",
  externalInsightsThemeSelected = "External Insights Theme Selected",
  investorMaterialsThemeSelected = "Investor Materials Theme Selected",
  externalInsightsArticleReadMoreClicked = "External Insights Article Read More Clicked",
  investorMaterialsArticleReadMoreClicked = "Investor Materials Article Read More Clicked",
  investorMaterialsPDFDownloadClicked = "Investor Materials PDF Download Clicked",
  researchSectionArticleReadMoreClicked = "Research Section Article Read More Clicked",
  researchSectionNavigationArrowClicked = "Research Section Navigation Arrow Clicked",
  teamMemberSocialIconClicked = "Team Member Social Icon Clicked",
  letsConnectSubmit = "Lets Connect Form Submitted",
  footerFollowUsClicked = "Footer Follow Us CTA Clicked",
  footerContactUsClicked = "Footer Contact Us CTA Clicked",
  chatBotOpened = "Chatbot opened",
  chatBotClosed = "Chatbot closed",
  visitorSaved = "Visitor Saved and Content unlocked",
  savedVisitorEmailEntered = "Saved Visitor Email Entered and Content unlocked",
  newVisitorEmailEntered = "New Visitor Email Entered",
  investorQuestionsSkipped = "Investor Questions Skipped and Content unlocked",
  externalInsightsFirstVideoPlayed = "External Insights First Video Played",
  externalInsightsSecondVideoPlayed = "External Insights Second Video Played",
  externalInsightsSpotifyPlayed = "External Insights Spotify Played",
  investorMaterialsVideoPlayed = "Investor Materials Video Played",
  investorStartedSignup = "Investor Started Signup",
  userOnboarded = "Investor Type Entered",
  onboardingSuccess = "Onboarding Success",
  InvestorFormSkipped = "Investor Form Skipped",
  investorLogin = "Investor Login",
  investorSignupInfo = "Investor Signup Info",
  showcaseInvestorSignUp = "Showcase Investor Sign Up Clicked",
  showcaseInvestorLogin = "Showcase Investor Login Clicked",
  showcasesPageView = "Discovery Page Viewed",
  investorSectionJoinClicked = "Investor Section Join Clicked",
  featuredShowcaseTeaserPlayed = "Featured Showcase Teaser Played",
  featuredShowcaseViewed = "Featured Showcase Opened",
  showcaseSearched = "Showcase Searched",
  showcaseTeaserPlayed = "Showcase Card Teaser Played",
  showcasePreviewOpened = "Showcase Card Preview Opened",
  showcaseCardClicked = "Showcase Card Opened",
  viewShowcaseWatchlistClicked = "View Showcase From Watchlist Clicked",
  showcaseCardCTAClicked = "Showcase CTA Clicked",
  requestShowcaseClicked = "Request Showcase Clicked",
  filtersModalOpened = "Filters Modal Opened",
  filtersModalClosed = "Filters Modal Closed",
  filtersApplied = "Filters Applied",
  filtersReset = "Filters Reset",
  watchlistFundamentalsTabClicked = "Watchlist Fundamentals Tab Clicked",
  watchlistChartTabClicked = "Watchlist Chart Tab Clicked",
  watchlistRemoveFromWatchlist = "Showcase Remove From Watchlist Clicked",
  watchlistUpdatedPriceFromWatchlist = "Updated Subscribed Price",
  watchlistTabOpened = "Watchlist Tab Opened",
  watchlistLeavingSignUp = "Dont Lose Watchlist Signup",
  watchlistLeavingLogin = "Dont Lose Watchlist Login",
  watchlistSidebarLogin = "Watchlist Sidebar Login",
  watchlistSidebarOpened = "Watchlist Sidebar Opened",
  watchlistSidebarClosed = "Watchlist Sidebar Closed",
  chartTimePeriod = "Chart time period",
  stockRatingSubmitted = "Stock Rating Submitted",
  buttonDisplayed = "A/B Button Displayed",
  surveyDataSubmitted = "Survey question selected",
}
export type UserEventProps = {
  mixpanelProps: Dict;
  id: string | number;
  eventName: MixpanelEventName;
};
export type CustomEventProps = {
  mixpanelProps: Dict;
  eventName: MixpanelEventName;
};
