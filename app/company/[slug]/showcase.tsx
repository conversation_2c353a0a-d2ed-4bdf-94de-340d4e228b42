'use client';

import {
  useEffect, useState,
} from "react";
import { Video } from "@/common/components/organisms/videoPlaylist/types";
import MixpanelPageView from "@/app/company/[slug]/sections/MixpanelPageView/MixpanelPageView";
import { TwitterConversionTrackingScript } from "@/common/utils/TwitterConversionTrackingScript";
import StocksSection from "@/app/company/[slug]/sections/StocksSection/StocksSection";
import Script from "next/script";
import { useSearchParams } from "next/navigation";
import { DefaultCurrency } from "@/app/cms/(protected)/showcase/[id]/hero-section/constants";
import { TrackVisitor } from "@/common/components/organisms/trackVisitor";
import ComingSoon from "@/app/company/[slug]/comingSoon";
import lang from "@/common/lang";
import dynamic from "next/dynamic";
import { Loader } from "@/common/components/molecules";
import { FbPixelScript } from "@/common/utils/fbPixel";
import { useSession } from "next-auth/react";
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import {
  taboolaCustomEvent, TaboolaEventName,
} from "@/common/utils/taboola/eventTriggers";
import toast from "react-hot-toast";
import { useGetWatchlist } from "@/app/watchlist/useGetWatchlist";
import { useAddToWatchlist } from "./hooks/useAddToWatchlist";
import { useUpdateWatchlist } from "@/common/hooks/useUpdateWatchlist";
import WatchlistSidebar from "./sections/WatchlistSidebar";
import { StockRatingModal } from "@/common/components/organisms/modal";
import {
  isRateStockEnabled,
  isSurvayEnabled,
  survayQuestions,
} from "@/common/constants";
import { useButtonDistribution } from "@/common/hooks/useButtonDistribution";
import {
  determineButtonDisplay, shouldShowBothButtons,
} from "@/common/utils/abTesting";
import mixpanel from "mixpanel-browser";
import {
  AiPromptType,
  ShowcaseType,
} from "./types";
import { SurveySection } from './sections/SurveySection';

const ExecutiveSummary = dynamic(() => import("@/app/company/[slug]/sections/InvestmentThesisSectionTabs/ExecutiveSummary").then(m => m.ExecutiveSummary), { ssr: false });
const InvestmentThesisSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.InvestmentThesisSection), { ssr: false });
const ExpertsSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.ExpertsSection), { ssr: false });
const DarkBanner = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.DarkBanner), { ssr: false });
const FAQSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.FAQSection), { ssr: false });
const CompanyContentSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.CompanyContentSection), { ssr: false });
const TitleSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.TitleSection), {
  ssr: false,
  loading: () => <Loader />,
});
const ExternalInsightsSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.ExternalInsightsSection), { ssr: false });
const LightBanner = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.LightBanner), { ssr: false });
const CompanyResearchSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.CompanyResearchSection), { ssr: false });
const ArticlesSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.ArticlesSection), { ssr: false });
const TeamSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.TeamSection), { ssr: false });
const LetsConnectSection = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.LetsConnectSection), { ssr: false });
const Footer = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.Footer), { ssr: false });
const ChatBot = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.ChatBot), { ssr: false });
const SectionTracker = dynamic(() => import("@/app/company/[slug]/sections").then(m => m.SectionTracker), { ssr: false });

export const maxDuration = 60

type Props = {
  slug: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: { [key: string]: any };
}

const {
  showcaseStatusTypes, showcase,
} = lang;

const Showcase = ({
  slug, data,
}: Props) => {
  const params = useSearchParams();
  const [isWatchlisted, setIsWatchlisted] = useState(false);
  const [isRatingModalOpen, setIsRatingModalOpen] = useState(false)
  const [watchlistCount, setWatchlistCount] = useState(0);
  const [isWatchlistSidebarOpen, setIsWatchlistSidebarOpen] = useState(false);
  const [watchlistUpdated, setWatchlistUpdated] = useState(false);
  const [showRateStockButton, setShowRateStockButton] = useState(false);
  const [showWatchlistButton, setShowWatchlistButton] = useState(true);
  const [hasUserVoted, setHasUserVoted] = useState(false);
  const [userVoteRating, setUserVoteRating] = useState<string | null>(null);

  const {
    data: session, status,
  } = useSession()
  const isAssistantAvailable = Boolean(data?.enable_curation_ai);
  const isComingSoon = data?.status === showcaseStatusTypes.inProgress;
  const preview = params.get('preview');
  const ceoInterviews = data?.ceo_interviews?.map((interview: Video & { video_iframe_url: string }) => {
    let url = interview?.video_iframe_url || '';
    url = url.split('?')[0];
    if (url.includes('youtube.com/embed/')) {
      const videoId = url.split('/embed/')[1];
      url = `https://www.youtube.com/watch?v=${videoId}`;
    }
    return {
      ...interview,
      url,
    };
  }) || []
  const {
    onSyncWatchlist,
  } = useUpdateWatchlist()
  const {
    isWatchlistLoading,
    mutateWatchlist,
    watchlist: watchlistList,
  } = useGetWatchlist()

  const {
    isAddingToWatchlist,
    onAddWatchlist,
  } = useAddToWatchlist()

  const {
    distribution, isLoading: isDistributionLoading,
  } = useButtonDistribution(slug);

  useEffect(() => {
    const asyncSyncWatchlist = async () => {
      if (status === "loading") {
        return
      }
      if (status === "authenticated" && session?.user?.role === "subscriber") {
        const localWatchlist = localStorage.getItem('watchlist')
        const list = localWatchlist ? JSON.parse(localWatchlist) : []
        if (list.length) {
          await onSyncWatchlist(session.user.email)
          mutateWatchlist()
        }
      }
    }
    asyncSyncWatchlist()
  }, [session, status, onSyncWatchlist, mutateWatchlist])

  useEffect(() => {
    if (status === "loading") {
      return
    }
    if (status === "authenticated" && session?.user?.role === "subscriber") {
      setWatchlistCount(watchlistList.length)
      watchlistList.forEach((item) => {
        if (item.showcase.slug === slug) {
          setIsWatchlisted(true)
          return
        }
      })
      return
    }
    const localWatchlist = localStorage.getItem('watchlist')
    const watchlist = localWatchlist ? JSON.parse(localWatchlist) : []
    setWatchlistCount(watchlist.length)
    if (watchlist.length > 0) {
      const isInWatchlist = watchlist.find((item: any) => item.slug === slug)
      if (isInWatchlist) {
        setIsWatchlisted(true)
        return
      }
    }
    setIsWatchlisted(false)
  }, [slug, session, status, watchlistList])

  useEffect(() => {
    if (status === "loading" || isDistributionLoading) {
      setShowWatchlistButton(false);
      setShowRateStockButton(false);
      return
    }

    if (isRateStockEnabled) {
      const isAuthenticated = status === "authenticated" && session?.user?.role === "subscriber";

      if (shouldShowBothButtons(isAuthenticated)) {
        setShowWatchlistButton(true);
        setShowRateStockButton(true);
      } else {
        const abTestResult = determineButtonDisplay(
          distribution,
          slug,
          mixpanel.get_distinct_id(),
        );

        setShowWatchlistButton(!abTestResult.shouldShowRateButton);
        setShowRateStockButton(abTestResult.shouldShowRateButton);
      }
    } else {
      setShowWatchlistButton(true);
      setShowRateStockButton(false);
    }
  }, [status, isDistributionLoading, distribution, slug, session])

  useEffect(() => {
    const checkExistingVote = () => {
      const userVotes = localStorage.getItem('stockRatingVotes');
      if (userVotes) {
        try {
          const votes = JSON.parse(userVotes);
          if (votes[slug]) {
            setHasUserVoted(true);
            setUserVoteRating(votes[slug].rating);
          } else {
            setHasUserVoted(false);
            setUserVoteRating(null);
          }
        } catch (error) {
          setHasUserVoted(false);
          setUserVoteRating(null);
        }
      } else {
        setHasUserVoted(false);
        setUserVoteRating(null);
      }
    };

    checkExistingVote();
  }, [slug])

  useEffect(() => {
    if (status === "authenticated" && session?.user?.role === "subscriber") {
      const shouldOpenModal = localStorage.getItem('openStockRatingModal');
      const cameFromRateShowcase = localStorage.getItem('cameFromRateShowcase');

      if (shouldOpenModal === 'true' && cameFromRateShowcase === 'true') {
        setTimeout(() => {
          setIsRatingModalOpen(true);
          localStorage.removeItem('openStockRatingModal');
        }, 500);
      }
    }
  }, [status, session])

  if (!data) {
    return (
      <Loader />
    );
  }

  const handleAddToWatchlist = async (source: string) => {
    taboolaCustomEvent({
      eventName: TaboolaEventName.AddToWatchlist,
      taboolaProps: {
        source: source,
        slug: slug,
        email: session?.user?.email || '',
      },
    });

    if (status === "authenticated" && session?.user?.role === "subscriber") {
      await onAddWatchlist({
        id: data?.showcase_id,
      })
      await mutateWatchlist()
      setWatchlistUpdated(prevState => !prevState)
    } else {
      const currentWatchlist = JSON.parse(localStorage.getItem('watchlist') || '[]')
      localStorage.setItem('watchlist', JSON.stringify([
        ...currentWatchlist,
        {
          slug,
          title: data?.title || '',
          logo: data?.logo,
          watchlistedPrice: data?.price || 0,
          followedSince: new Date().toString(),
        },
      ]))
      setWatchlistUpdated(prevState => !prevState)
      toast.success(showcase.addToWatchlist)
    }
    mixpanelCustomEvent({
      eventName: MixpanelEventName.followUsClicked,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
        source: source,
      },
    })
  }

  const handleRateStock = (source: string) => {
    setIsRatingModalOpen(true);
    mixpanelCustomEvent({
      eventName: MixpanelEventName.rateShowcaseClicked,
      mixpanelProps: {
        page: 'Showcase',
        slug: slug,
        source: source,
      },
    })
  }

  const toggleWatchlistSidebar = () => {
    setIsWatchlistSidebarOpen(prev => !prev);
    if (watchlistUpdated) {
      setWatchlistUpdated(false);
    }
  }

  const getRatingIcon = (rating: string) => {
    switch (rating) {
    case 'buy':
      return '💰';
    case 'hold':
      return '✋';
    case 'sell':
      return '💸';
    default:
      return '💰';
    }
  };

  const primaryCTALabel = () => {
    if (data?.private_showcase) {
      return data?.hero_cta_label
    }
    if (isWatchlisted) {
      return 'Following Stock'
    }
    return '+ Watchlist'
  }

  const rateStockLabel = () => {
    if (hasUserVoted && userVoteRating) {
      return `Update vote (${getRatingIcon(userVoteRating)})`;
    }
    return "Rate the Stock";
  }
  const isGoogleFinanceEnabled = data?.is_google_finance_enabled
  return (isComingSoon && !preview ?
    (<ComingSoon title={data?.title} setIsWatchlisted={setIsWatchlisted} isWatchlisted={isWatchlisted} onAddWatchlist={handleAddToWatchlist} />)
    : (
      <div className={`flex gap-[30px] md:gap-[50px] min-h-screen flex-col justify-center items-center antialiased pt-[73px]`}>
        {(isWatchlistLoading || isAddingToWatchlist) && <Loader />}
        <MixpanelPageView slug={slug} title={data?.title} />
        <SectionTracker />
        <TwitterConversionTrackingScript />
        <FbPixelScript />
        <Script
          id="twitter-conversion-tracking"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-TTEGBHDD0J');
          `,
          }}
        />
        {
          data?.showcase_heading ? (
            <TitleSection
              enableCurationAI={isAssistantAvailable}
              companyName={data?.title || ''}
              privateShowcase={data?.private_showcase}
              teaserVideoLabel={data?.teaser_video_label}
              handleAddToWatchlist={handleAddToWatchlist}
              handleRateStock={handleRateStock}
              primaryCTALabel={primaryCTALabel}
              rateStockLabel={rateStockLabel}
              currencySymbol={data?.currency_symbol}
              price={isGoogleFinanceEnabled ? data?.price : data?.stock_price}
              showRateStockButton={showRateStockButton}
              isGoogleFinanceEnabled={isGoogleFinanceEnabled}
              showWatchlistButton={showWatchlistButton}
              stockSymbol={data?.stock_symbol}
              setIsWatchlisted={setIsWatchlisted}
              watchlistCount={watchlistCount}
              isWatchlisted={isWatchlisted}
              updatedAt={data?.updated_at}
              teaserVideoLink={data?.teaser_video || ""}
              teaserVideoTitle={data?.teaser_video_title}
              teaserVideoCaption={data?.teaser_video_caption}
              exchange={data?.exchange}
              industries={data?.industries || []}
              tags={data?.tag}
              discoveryCoverImage={data?.discovery_cover_image || ''}
              symbol={data?.symbol}
              logo={data?.logo}
              changePercent={data?.change_percent}
              graphLink={data?.graph_link}
              subtitle={data?.hero_caption}
              title={data?.showcase_heading}
              heroCTALink={data?.hero_cta_link}
              heroCTALabel={data?.hero_cta_label}
              slug={slug}
              buy={data?.buy || []}
              sell={data?.sell || []}
              prompts={data?.prompts ? data?.prompts.sort((a: AiPromptType, b: AiPromptType) => a.sequence - b.sequence) : []}
            />
          ) : null
        }
        <div className="w-full gap-[48px] md:gap-[100px] flex flex-col justify-center items-center">
          <div className="relative flex flex-col w-full justify-center items-center">
            {
              (data?.private_showcase ? (!data?.raise_amount || !data?.equity_offered || !data?.valuation) : (!data?.exchange || !data?.symbol)) ? null : (
                <StocksSection
                  privateShowcase={data?.private_showcase}
                  raiseAmount={data?.raise_amount}
                  raiseAmountCurrency={data?.raise_amount_currency || DefaultCurrency}
                  equityOffered={data?.equity_offered}
                  valuation={data?.valuation}
                  currencySymbol={data?.currency_symbol}
                  valuationCurrency={data?.valuation_currency || DefaultCurrency}
                  revenue={data?.revenue}
                  targetPriceChange={data?.target_price_change_percentage}
                  revenueCurrency={data?.revenue_currency || DefaultCurrency}
                  marketOpportunity={data?.market_opportunity}
                  targetPrice={data?.target_price || 0}
                  marketOpportunityCurrency={data?.market_opportunity_currency || DefaultCurrency}
                  exchange={data?.exchange}
                  stockSymbol={data?.stock_symbol}
                  isGoogleFinanceEnabled={isGoogleFinanceEnabled}
                  priceTargetDisclaimer={data?.price_target_disclaimer}
                  symbol={data?.symbol}
                  price={isGoogleFinanceEnabled ? data?.price : data?.stock_price}
                  changePercent={data?.change_percent}
                  marketCap={data?.marketcap}
                  PERatio={data?.pe_ratio}
                  averageVolume={data?.average_volume}
                />
              )
            }
          </div>
          {isSurvayEnabled && (
            <div className="w-full">
              <SurveySection
                slug={slug}
                questions={survayQuestions}
              />
            </div>
          )}
          <ExecutiveSummary
            ceoInterView={ceoInterviews && (ceoInterviews[0]?.url || ceoInterviews[0]?.video)}
            title={data?.executive_summary_title}
            content={data?.executive_summary}
            investorRelationsLabel={data?.investor_relations_label}
            investorRelationsLink={data?.investor_relations_link}
            slug={slug}
            privateShowcase={data?.private_showcase}
          />
          <InvestmentThesisSection
            data={{
              title: data?.executive_summary_title,
              content: data?.executive_summary,
              showcaseId: data?.showcase_id,
              graphLink: data?.graph_link || '',
              keyRisks: data?.key_risks,
              investorRelationsLink: data?.investor_relations_link,
              investorRelationsLabel: data?.investor_relations_label,
              usps: data?.usps,
              catalysts: data?.catalysts,
              ceoInterviews: data?.ceo_interviews?.map((interview: Video & { video_iframe_url: string }) => ({
                ...interview,
                url: interview?.video_iframe_url,
              })) || [],
            }}
            type={data?.type as ShowcaseType}
            slug={slug}
            privateShowcase={data?.private_showcase}
          />
          <div className="bg-dark-background w-full flex flex-col gap-16 md:gap-32 justify-center items-center">
            <ExpertsSection experts={data?.experts} slug={slug} />
            <DarkBanner
              setIsWatchlisted={setIsWatchlisted}
              slug={slug}
              isWatchlisted={isWatchlisted}
              handleAddToWatchlist={handleAddToWatchlist}
              privateShowcase={data?.private_showcase}
              toggleWatchlistSidebar={toggleWatchlistSidebar}
            />
            <FAQSection data={data?.faqs} slug={slug} privateShowcase={data?.private_showcase} />
          </div>
          <div className="md:pt-16 w-full flex justify-center">
            <CompanyContentSection content={data?.company_contents} slug={slug} />
          </div>
          <div className="w-full flex flex-col justify-center items-center pt-8 md:pt-[80px] pb-8 md:pb-16 gap-8 md:gap-[100px]">
            <LightBanner
              setIsWatchlisted={setIsWatchlisted}
              slug={slug}
              isWatchlisted={isWatchlisted}
              handleAddToWatchlist={handleAddToWatchlist}
              privateShowcase={data?.private_showcase}
              toggleWatchlistSidebar={toggleWatchlistSidebar}
            />
            <ExternalInsightsSection content={data?.external_insights} slug={slug} />
          </div>
          <CompanyResearchSection url={data?.researches?.[0]?.link || ''} />
          <ArticlesSection articles={data?.articles} slug={slug} />
          <TeamSection team={data?.team_members} slug={slug} />
          <LetsConnectSection privateShowcase={data?.private_showcase} slug={slug} />
          <Footer slug={slug} disclaimer={data?.disclaimer} />
          {data?.project_id && data?.agent_id && (
            <ChatBot slug={slug} title={data?.title} projectId={data?.project_id} agentId={data?.agent_id} intent={data?.intent || ''} />
          )}
          <StockRatingModal
            isOpen={isRatingModalOpen}
            onClose={() => {
              setIsRatingModalOpen(false);
              const userVotes = localStorage.getItem('stockRatingVotes');
              if (userVotes) {
                try {
                  const votes = JSON.parse(userVotes);
                  if (votes[slug]) {
                    setHasUserVoted(true);
                    setUserVoteRating(votes[slug].rating);
                  } else {
                    setHasUserVoted(false);
                    setUserVoteRating(null);
                  }
                } catch (error) {
                  setHasUserVoted(false);
                  setUserVoteRating(null);
                }
              } else {
                setHasUserVoted(false);
                setUserVoteRating(null);
              }
            }}
            companyName={data?.title || ''}
            slug={data?.slug || slug}
          />
          <TrackVisitor slug={slug} />
          <WatchlistSidebar
            isOpen={isWatchlistSidebarOpen}
            onClose={() => setIsWatchlistSidebarOpen(false)}
            source="banner"
            currencySymbol={data?.currency_symbol}
            isGoogleFinanceEnabled={isGoogleFinanceEnabled}
          />
        </div>
      </div>
    ));
}

export default Showcase;