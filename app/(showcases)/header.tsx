'use client';
import Link from "next/link";
import {
  authToken,
  homePageLink,
  loggedinUserUuid,
  surveyAnswers,
} from "@/common/constants";
import { MenuIcon } from "@/common/components/icons/MenuIcon";
import { Popover } from "@headlessui/react";
import useSWRMutation from "swr/mutation";
import {
  mixpanelCustomEvent, MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import lang from '@/common/lang';
import { routes } from "@/common/routes";
import { fetcher } from "@/common/utils/network/baseFetcher";
import {
  signOut,
  useSession,
} from "next-auth/react";
import {
  getLocalStorageItem,
  removeLocalStorageItem,
} from "@/common/utils/helpers";
import { logoutUrl } from "@/common/utils/network/endpoints";
import { httpRequestMethods } from "@/common/utils/network/constants";
import Image from "next/image";
import {
  useEffect, useRef,
} from "react";
import { useWindowDimensions } from "@/common/hooks";
import { LogoIcon } from "@/common/components/icons";
import HeaderItems from "./headerItems";
import { User } from "lucide-react";
import { usePathname } from "next/navigation";

const { POST } = httpRequestMethods;

const {
  dashboard: {
    showcase: {
      header,
    },
  },
} = lang

export const Header = ({ watchlistCount } : { watchlistCount: number }) => {
  const { data: session } = useSession()
  const { isDesktopView } = useWindowDimensions()
  const pathname = usePathname()
  const signOutOnBE = useSWRMutation(
    logoutUrl, fetcher);
  const { trigger } = signOutOnBE;
  const effectRan = useRef(false);
  const handleLogout = async () => {
    const token = getLocalStorageItem(authToken)
    if (token) {
      await trigger({
        method: POST,
      });
    }
    await signOut({ redirect: false });
    removeLocalStorageItem(authToken);
    removeLocalStorageItem(loggedinUserUuid);
    removeLocalStorageItem(surveyAnswers);
  };
  useEffect(() => {
    if (effectRan.current === false) {
      mixpanelCustomEvent({
        eventName: MixpanelEventName.pageView,
        mixpanelProps: {
          page: pathname === "/" ? 'Shareflix' : "Watchlist",
        },
      });

      effectRan.current = true;
    }
  }, [pathname]);

  const handleLogin = () => {
    mixpanelCustomEvent({
      eventName: MixpanelEventName.showcaseInvestorLogin,
      mixpanelProps: {
        page: pathname === "/" ? 'Shareflix' : "Watchlist",
      },
    })
    sessionStorage.setItem('redirect', window.location.href)
  }
  const handleSignUp = () => {
    mixpanelCustomEvent({
      eventName: MixpanelEventName.showcaseInvestorSignUp,
      mixpanelProps: {
        page: pathname === "/" ? 'Shareflix' : "Watchlist",
      },
    })
    sessionStorage.setItem('redirect', window.location.href)
  }
  return (
    <div
      className={`flex justify-center fixed top-0 left-0 right-0 w-full mx-auto bg-[#0E0E0F] border-b-[1px] border-[#3F3D42] z-20`}
    >
      <div className="py-4 sm:px-8 px-4 w-full flex justify-between items-center relative">
        <div className="flex items-center justify-start gap-8">
          <Link href={homePageLink}>
            {isDesktopView ? <Image src="/static/CurationConnectLogo.svg" width={77} height={32} alt="Curation Connect" /> : <LogoIcon />}
          </Link>
          <div className="hidden xl:block absolute left-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2">
            <HeaderItems
              handleLogout={handleLogout}
              watchlistCount={watchlistCount}
            />
          </div>
        </div>
        <div className="flex items-center gap-2 lg:gap-4">
          {session?.user.role === "subscriber" ? (
            <Popover className="relative hidden xl:flex items-center justify-center">
              <Popover.Button
                className="bg-white w-8 h-8 rounded-full"
                data-cy="user-button"
              >
                <span className="text-sm font-medium text-[#3F3D42] uppercase">
                  {session.user?.firstName ? session.user.firstName.trim().charAt(0) : <span className="flex w-full h-full justify-center items-center"><User width={18} height={18} /></span>}
                </span>
              </Popover.Button>
              <Popover.Panel className="absolute top-[calc(100%+8px)] right-0 z-30 w-auto">
                {({ close }) => (
                  <div className="flex flex-col gap-1 bg-card-bg-secondary p-2 items-center rounded-lg">
                    {
                      <button
                        data-cy="logout-button"
                        onClick={() => {
                          handleLogout();
                          close();
                        }}
                        className="w-full"
                      >
                        <div className="py-3 px-4 font-medium text-base text-center w-full leading-none text-header-link-item hover:bg-white hover:bg-opacity-10 rounded-md hover:text-white transition-all whitespace-nowrap">
                          {header.logout}
                        </div>
                      </button>
                    }
                  </div>
                )}
              </Popover.Panel>
            </Popover>
          ) : (
            <>
              <Link
                onClick={handleLogin}
                data-cy="login"
                id="login-button"
                href={`${routes.showcaseLogin}`}
                className="w-full"
              >
                <div className="py-2.5 lg:py-[7px] px-4 font-medium text-sm lg:text-base text-center w-full leading-none text-header-link-item hover:bg-white hover:bg-opacity-10 rounded-md hover:text-white transition-all whitespace-nowrap">
                  {header.login}
                </div>
              </Link>
              <Link
                onClick={handleSignUp}
                data-cy="signup"
                id="signup-button"
                href={`${routes.showcaseSignUp}`}
                className="w-full"
              >
                <div className="py-2.5 lg:py-[7px] px-4 font-medium text-sm lg:text-base text-center w-full leading-none bg-white text-[#3F3D42] hover:bg-white/85 rounded-md  transition-all whitespace-nowrap">
                  {header.signup}
                </div>
              </Link>
            </>
          )}
          <div className="xl:hidden flex items-center gap-3">
            <Popover className="relative flex items-center justify-center">
              <Popover.Button>
                {" "}
                <MenuIcon />
              </Popover.Button>
              <Popover.Panel className="absolute top-[calc(100%+8px)] right-0 z-30 w-auto">
                {({ close }) => (
                  <HeaderItems
                    watchlistCount={watchlistCount}
                    onClick={() => close()}
                    handleLogout={handleLogout}
                  />
                )}
              </Popover.Panel>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
};
